#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ORC处理服务客户端脚本

用于通过API接口控制ORC文件自动处理功能
支持启动、停止、状态查询等操作
"""

import argparse
import json
import sys
import requests
from datetime import datetime
from typing import Optional, List


class ORCProcessingClient:
    """ORC处理服务客户端"""
    
    def __init__(self, host: str = "localhost", port: int = 42001):
        self.host = host
        self.port = port
        self.base_url = f"http://{host}:{port}"
    
    def _make_request(self, method: str, endpoint: str, data: Optional[dict] = None) -> dict:
        """发送HTTP请求"""
        url = f"{self.base_url}{endpoint}"
        
        try:
            if method.upper() == "GET":
                response = requests.get(url, timeout=10)
            elif method.upper() == "POST":
                response = requests.post(url, json=data, timeout=10)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.ConnectionError:
            print(f"❌ 无法连接到服务 {self.host}:{self.port}")
            print("请确保服务已启动:")
            print(f"  python3 -m services.orc_mongodb_service.orc_processor_service.main --port {self.port}")
            sys.exit(1)
        except requests.exceptions.Timeout:
            print(f"❌ 请求超时 {url}")
            sys.exit(1)
        except requests.exceptions.HTTPError as e:
            print(f"❌ HTTP错误: {e}")
            sys.exit(1)
        except Exception as e:
            print(f"❌ 请求失败: {e}")
            sys.exit(1)
    
    def health_check(self) -> dict:
        """健康检查"""
        print("🔍 执行健康检查...")
        result = self._make_request("GET", "/health")
        
        status = result.get("status", "unknown")
        if status == "healthy":
            print("✅ 服务健康")
        else:
            print("⚠️ 服务状态异常")
        
        return result
    
    def get_status(self) -> dict:
        """获取服务状态"""
        print("📊 获取服务状态...")
        result = self._make_request("GET", "/stats")
        
        stats = result.get("stats", {})
        is_processing = result.get("is_processing", False)
        
        print(f"🔄 处理状态: {'进行中' if is_processing else '空闲'}")
        print(f"📁 已处理文件: {stats.get('processed_files', 0)}/{stats.get('total_files', 0)}")
        print(f"👥 已处理用户: {stats.get('processed_users', 0)}")
        
        return result
    
    def get_queue_status(self) -> dict:
        """获取队列状态"""
        print("📋 获取队列状态...")
        result = self._make_request("GET", "/queue/status")
        
        queue_length = result.get("queue_length", 0)
        is_paused = result.get("is_paused", False)
        
        print(f"📦 队列长度: {queue_length}")
        print(f"⏸️ 队列状态: {'暂停' if is_paused else '运行中'}")
        
        return result
    
    def start_processing(self, start_date: Optional[str] = None, 
                        end_date: Optional[str] = None, 
                        province_ids: Optional[List[int]] = None) -> dict:
        """启动处理"""
        print("🚀 启动ORC文件自动处理...")
        
        # 构建请求数据
        data = {}
        if start_date:
            data["start_date"] = start_date
        if end_date:
            data["end_date"] = end_date
        if province_ids:
            data["province_ids"] = province_ids
        
        if data:
            print("📋 使用参数:")
            for key, value in data.items():
                print(f"   {key}: {value}")
        else:
            print("📋 使用默认配置参数")
        
        result = self._make_request("POST", "/start-processing", data)
        
        status = result.get("status", "unknown")
        message = result.get("message", "")
        
        if status == "started":
            print("✅ 处理已成功启动")
            print("💡 提示: 使用 --status 查看处理进度")
        elif status == "already_running":
            print("⚠️ 处理已在进行中")
        elif status == "error":
            print(f"❌ 启动失败: {message}")
        else:
            print(f"⚠️ 未知状态: {status}")
        
        return result
    
    def stop_processing(self) -> dict:
        """停止处理"""
        print("⏹️ 停止ORC处理...")
        result = self._make_request("POST", "/stop-processing")
        
        status = result.get("status", "unknown")
        if status == "stopped":
            print("✅ 处理已停止")
        else:
            print(f"⚠️ 停止状态: {status}")
        
        return result


def validate_date(date_str: str) -> bool:
    """验证日期格式"""
    try:
        datetime.strptime(date_str, "%Y%m%d")
        return True
    except ValueError:
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="ORC处理服务客户端",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 健康检查
  python3 scripts/orc_processing_client.py --health
  
  # 查看状态
  python3 scripts/orc_processing_client.py --status
  
  # 使用默认配置启动
  python3 scripts/orc_processing_client.py --start
  
  # 指定日期范围启动
  python3 scripts/orc_processing_client.py --start -s 20250701 -e 20250705
  
  # 指定省份启动
  python3 scripts/orc_processing_client.py --start -P 100 200 300
  
  # 指定日期和省份启动
  python3 scripts/orc_processing_client.py --start -s 20250701 -e 20250705 -P 100 200
  
  # 停止处理
  python3 scripts/orc_processing_client.py --stop
        """
    )
    
    # 服务连接参数
    parser.add_argument("--host", default="localhost", help="服务主机地址")
    parser.add_argument("--port", type=int, default=42001, help="服务端口")
    
    # 操作参数
    parser.add_argument("--health", action="store_true", help="健康检查")
    parser.add_argument("--status", action="store_true", help="查看状态")
    parser.add_argument("--queue", action="store_true", help="查看队列状态")
    parser.add_argument("--start", action="store_true", help="启动处理")
    parser.add_argument("--stop", action="store_true", help="停止处理")
    
    # 处理参数
    parser.add_argument("-s", "--start-date", help="开始日期 (YYYYMMDD格式)")
    parser.add_argument("-e", "--end-date", help="结束日期 (YYYYMMDD格式)")
    parser.add_argument("-P", "--provinces", type=int, nargs="+", help="省份ID列表")
    
    # 输出格式
    parser.add_argument("--json", action="store_true", help="以JSON格式输出")
    
    args = parser.parse_args()
    
    # 验证参数
    if args.start_date and not validate_date(args.start_date):
        print("❌ 开始日期格式错误，应为YYYYMMDD格式")
        sys.exit(1)
    
    if args.end_date and not validate_date(args.end_date):
        print("❌ 结束日期格式错误，应为YYYYMMDD格式")
        sys.exit(1)
    
    if args.provinces and any(p <= 0 for p in args.provinces):
        print("❌ 省份ID必须是正整数")
        sys.exit(1)
    
    # 创建客户端
    client = ORCProcessingClient(args.host, args.port)
    
    # 执行操作
    result = None
    
    if args.health:
        result = client.health_check()
    elif args.status:
        result = client.get_status()
    elif args.queue:
        result = client.get_queue_status()
    elif args.start:
        result = client.start_processing(args.start_date, args.end_date, args.provinces)
    elif args.stop:
        result = client.stop_processing()
    else:
        # 默认显示状态
        result = client.get_status()
    
    # 输出结果
    if args.json and result:
        print("\n" + "="*50)
        print("JSON响应:")
        print(json.dumps(result, indent=2, ensure_ascii=False))


if __name__ == "__main__":
    main()
