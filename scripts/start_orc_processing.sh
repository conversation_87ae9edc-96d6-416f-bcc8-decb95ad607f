#!/bin/bash
# -*- coding: utf-8 -*-
"""
ORC处理服务启动脚本

用于通过API接口启动ORC文件自动处理功能
支持自定义日期范围和省份范围参数
"""

# 默认配置
DEFAULT_HOST="localhost"
DEFAULT_PORT="42001"
DEFAULT_START_DATE=""
DEFAULT_END_DATE=""
DEFAULT_PROVINCES=""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示帮助信息
show_help() {
    echo -e "${BLUE}ORC处理服务启动脚本${NC}"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  -H, --host HOST         服务主机地址 (默认: $DEFAULT_HOST)"
    echo "  -p, --port PORT         服务端口 (默认: $DEFAULT_PORT)"
    echo "  -s, --start-date DATE   开始日期 YYYYMMDD格式 (可选)"
    echo "  -e, --end-date DATE     结束日期 YYYYMMDD格式 (可选)"
    echo "  -P, --provinces IDS     省份ID列表，逗号分隔 (可选，如: 100,200,300)"
    echo "  --status                查看服务状态"
    echo "  --stop                  停止处理"
    echo "  --health                健康检查"
    echo ""
    echo "示例:"
    echo "  $0                                          # 使用默认配置启动"
    echo "  $0 -s 20250701 -e 20250705                 # 指定日期范围"
    echo "  $0 -P 100,200,300                          # 指定省份"
    echo "  $0 -s 20250701 -e 20250705 -P 100,200      # 指定日期和省份"
    echo "  $0 --status                                 # 查看状态"
    echo "  $0 --stop                                   # 停止处理"
    echo ""
}

# 验证日期格式
validate_date() {
    local date=$1
    if [[ ! $date =~ ^[0-9]{8}$ ]]; then
        echo -e "${RED}错误: 日期格式不正确，应为YYYYMMDD格式${NC}"
        return 1
    fi
    
    # 验证日期是否有效
    if ! date -d "$date" >/dev/null 2>&1; then
        echo -e "${RED}错误: 无效的日期 $date${NC}"
        return 1
    fi
    
    return 0
}

# 验证省份ID
validate_provinces() {
    local provinces=$1
    if [[ ! $provinces =~ ^[0-9]+(,[0-9]+)*$ ]]; then
        echo -e "${RED}错误: 省份ID格式不正确，应为数字，多个用逗号分隔${NC}"
        return 1
    fi
    return 0
}

# 检查服务是否可用
check_service() {
    local host=$1
    local port=$2
    
    echo -e "${YELLOW}检查服务连接 ${host}:${port}...${NC}"
    
    if ! curl -s --connect-timeout 5 "http://${host}:${port}/health" >/dev/null; then
        echo -e "${RED}错误: 无法连接到服务 ${host}:${port}${NC}"
        echo -e "${YELLOW}请确保服务已启动:${NC}"
        echo "  python3 -m services.orc_mongodb_service.orc_processor_service.main --port ${port}"
        return 1
    fi
    
    echo -e "${GREEN}✓ 服务连接正常${NC}"
    return 0
}

# 获取服务状态
get_status() {
    local host=$1
    local port=$2
    
    echo -e "${BLUE}获取服务状态...${NC}"
    
    local response=$(curl -s "http://${host}:${port}/stats")
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}服务状态:${NC}"
        echo "$response" | python3 -m json.tool 2>/dev/null || echo "$response"
    else
        echo -e "${RED}获取状态失败${NC}"
        return 1
    fi
}

# 健康检查
health_check() {
    local host=$1
    local port=$2
    
    echo -e "${BLUE}执行健康检查...${NC}"
    
    local response=$(curl -s "http://${host}:${port}/health")
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}健康检查结果:${NC}"
        echo "$response" | python3 -m json.tool 2>/dev/null || echo "$response"
    else
        echo -e "${RED}健康检查失败${NC}"
        return 1
    fi
}

# 停止处理
stop_processing() {
    local host=$1
    local port=$2
    
    echo -e "${YELLOW}停止ORC处理...${NC}"
    
    local response=$(curl -s -X POST "http://${host}:${port}/stop-processing")
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}停止请求已发送:${NC}"
        echo "$response" | python3 -m json.tool 2>/dev/null || echo "$response"
    else
        echo -e "${RED}停止请求失败${NC}"
        return 1
    fi
}

# 启动处理
start_processing() {
    local host=$1
    local port=$2
    local start_date=$3
    local end_date=$4
    local provinces=$5
    
    echo -e "${BLUE}启动ORC文件自动处理...${NC}"
    
    # 构建JSON请求体
    local json_body="{}"
    
    if [ -n "$start_date" ]; then
        json_body=$(echo "$json_body" | python3 -c "
import sys, json
data = json.load(sys.stdin)
data['start_date'] = '$start_date'
print(json.dumps(data))
")
    fi
    
    if [ -n "$end_date" ]; then
        json_body=$(echo "$json_body" | python3 -c "
import sys, json
data = json.load(sys.stdin)
data['end_date'] = '$end_date'
print(json.dumps(data))
")
    fi
    
    if [ -n "$provinces" ]; then
        # 将逗号分隔的字符串转换为数组
        local province_array="[$(echo "$provinces" | sed 's/,/,/g')]"
        json_body=$(echo "$json_body" | python3 -c "
import sys, json
data = json.load(sys.stdin)
data['province_ids'] = [int(x) for x in '$provinces'.split(',')]
print(json.dumps(data))
")
    fi
    
    echo -e "${YELLOW}请求参数:${NC}"
    echo "$json_body" | python3 -m json.tool 2>/dev/null || echo "$json_body"
    echo ""
    
    # 发送请求
    local response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "$json_body" \
        "http://${host}:${port}/start-processing")
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}启动请求已发送:${NC}"
        echo "$response" | python3 -m json.tool 2>/dev/null || echo "$response"
        
        # 检查响应状态
        local status=$(echo "$response" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    print(data.get('status', 'unknown'))
except:
    print('unknown')
" 2>/dev/null)
        
        if [ "$status" = "started" ]; then
            echo ""
            echo -e "${GREEN}✓ ORC处理已成功启动${NC}"
            echo -e "${YELLOW}提示: 使用 '$0 --status' 查看处理进度${NC}"
        elif [ "$status" = "already_running" ]; then
            echo ""
            echo -e "${YELLOW}⚠ 处理已在进行中${NC}"
        else
            echo ""
            echo -e "${RED}✗ 启动可能失败，请检查响应信息${NC}"
        fi
    else
        echo -e "${RED}启动请求失败${NC}"
        return 1
    fi
}

# 主函数
main() {
    local host=$DEFAULT_HOST
    local port=$DEFAULT_PORT
    local start_date=$DEFAULT_START_DATE
    local end_date=$DEFAULT_END_DATE
    local provinces=$DEFAULT_PROVINCES
    local action="start"
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -H|--host)
                host="$2"
                shift 2
                ;;
            -p|--port)
                port="$2"
                shift 2
                ;;
            -s|--start-date)
                start_date="$2"
                shift 2
                ;;
            -e|--end-date)
                end_date="$2"
                shift 2
                ;;
            -P|--provinces)
                provinces="$2"
                shift 2
                ;;
            --status)
                action="status"
                shift
                ;;
            --stop)
                action="stop"
                shift
                ;;
            --health)
                action="health"
                shift
                ;;
            *)
                echo -e "${RED}未知选项: $1${NC}"
                echo "使用 -h 或 --help 查看帮助信息"
                exit 1
                ;;
        esac
    done
    
    # 验证参数
    if [ -n "$start_date" ] && ! validate_date "$start_date"; then
        exit 1
    fi
    
    if [ -n "$end_date" ] && ! validate_date "$end_date"; then
        exit 1
    fi
    
    if [ -n "$provinces" ] && ! validate_provinces "$provinces"; then
        exit 1
    fi
    
    # 检查服务连接
    if ! check_service "$host" "$port"; then
        exit 1
    fi
    
    # 执行相应操作
    case $action in
        "start")
            start_processing "$host" "$port" "$start_date" "$end_date" "$provinces"
            ;;
        "status")
            get_status "$host" "$port"
            ;;
        "stop")
            stop_processing "$host" "$port"
            ;;
        "health")
            health_check "$host" "$port"
            ;;
    esac
}

# 执行主函数
main "$@"
