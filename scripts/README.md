# ORC处理服务客户端脚本

本目录包含用于控制ORC处理服务的客户端脚本，支持通过API接口启动、停止和监控ORC文件处理任务。

## 脚本列表

### 1. `orc_processing_client.py` - Python客户端（推荐）
功能完整的Python客户端，支持所有API操作，提供友好的命令行界面。

### 2. `start_orc_processing.sh` - Shell脚本
功能完整的Shell脚本，使用curl调用API，适合在shell环境中使用。

### 3. `example_usage.sh` - 使用示例
演示各种使用方式的示例脚本，包含完整的工作流程。

## 快速开始

### 1. 启动ORC处理服务
```bash
# 在一个终端中启动服务
cd /path/to/User-DF
USER_DF_CONFIG_FILE=configs/orc_mongodb_service/orc_processor_service/development.yaml \
python3 -m services.orc_mongodb_service.orc_processor_service.main --port 42001
```

### 2. 使用Python客户端（推荐）

#### 健康检查
```bash
python3 scripts/orc_processing_client.py --health
```

#### 查看服务状态
```bash
python3 scripts/orc_processing_client.py --status
```

#### 启动处理（使用默认配置）
```bash
python3 scripts/orc_processing_client.py --start
```

#### 启动处理（指定日期范围）
```bash
python3 scripts/orc_processing_client.py --start -s 20250701 -e 20250705
```

#### 启动处理（指定省份）
```bash
python3 scripts/orc_processing_client.py --start -P 100 200 300
```

#### 启动处理（指定日期和省份）
```bash
python3 scripts/orc_processing_client.py --start -s 20250701 -e 20250705 -P 100 200
```

#### 停止处理
```bash
python3 scripts/orc_processing_client.py --stop
```

#### 获取JSON格式输出
```bash
python3 scripts/orc_processing_client.py --status --json
```

### 3. 使用Shell脚本

#### 查看帮助
```bash
./scripts/start_orc_processing.sh --help
```

#### 健康检查
```bash
./scripts/start_orc_processing.sh --health
```

#### 启动处理（指定参数）
```bash
./scripts/start_orc_processing.sh -s 20250701 -e 20250705 -P 100,200,300
```

#### 停止处理
```bash
./scripts/start_orc_processing.sh --stop
```

### 4. 直接使用curl

#### 健康检查
```bash
curl -s http://localhost:42001/health | python3 -m json.tool
```

#### 启动处理（默认配置）
```bash
curl -s -X POST -H "Content-Type: application/json" -d '{}' \
  http://localhost:42001/start-processing | python3 -m json.tool
```

#### 启动处理（自定义参数）
```bash
curl -s -X POST -H "Content-Type: application/json" \
  -d '{"start_date":"20250701","end_date":"20250705","province_ids":[100,200,300]}' \
  http://localhost:42001/start-processing | python3 -m json.tool
```

#### 停止处理
```bash
curl -s -X POST http://localhost:42001/stop-processing | python3 -m json.tool
```

## API接口说明

### `/start-processing` - 启动处理
- **方法**: POST
- **参数**:
  - `start_date` (可选): 开始日期，YYYYMMDD格式
  - `end_date` (可选): 结束日期，YYYYMMDD格式
  - `province_ids` (可选): 省份ID列表，整数数组
- **响应**: 启动状态和参数信息

### `/stop-processing` - 停止处理
- **方法**: POST
- **响应**: 停止状态

### `/health` - 健康检查
- **方法**: GET
- **响应**: 服务健康状态

### `/stats` - 获取统计信息
- **方法**: GET
- **响应**: 详细的处理统计信息

### `/queue/status` - 获取队列状态
- **方法**: GET
- **响应**: Redis队列状态信息

## 参数说明

### 日期参数
- 格式: YYYYMMDD（如：20250701）
- start_date: 处理开始日期
- end_date: 处理结束日期
- 如果不指定，使用配置文件中的默认值

### 省份参数
- 格式: 正整数列表
- Python客户端: `-P 100 200 300`
- Shell脚本: `-P 100,200,300`
- curl: `"province_ids":[100,200,300]`
- 如果不指定，使用配置文件中的默认值

## 工作流程示例

### 完整的处理流程
```bash
# 1. 启动服务（在后台运行）
USER_DF_CONFIG_FILE=configs/orc_mongodb_service/orc_processor_service/development.yaml \
python3 -m services.orc_mongodb_service.orc_processor_service.main --port 42001 &

# 2. 等待服务启动
sleep 5

# 3. 健康检查
python3 scripts/orc_processing_client.py --health

# 4. 启动处理（指定日期和省份）
python3 scripts/orc_processing_client.py --start -s 20250701 -e 20250705 -P 100 200

# 5. 监控处理进度
while true; do
    python3 scripts/orc_processing_client.py --status
    sleep 30
done

# 6. 如需停止处理
python3 scripts/orc_processing_client.py --stop
```

### 批量处理多个日期范围
```bash
# 处理多个日期范围
dates=(
    "20250701 20250705"
    "20250706 20250710"
    "20250711 20250715"
)

for date_range in "${dates[@]}"; do
    start_date=$(echo $date_range | cut -d' ' -f1)
    end_date=$(echo $date_range | cut -d' ' -f2)
    
    echo "处理日期范围: $start_date - $end_date"
    python3 scripts/orc_processing_client.py --start -s $start_date -e $end_date -P 100 200
    
    # 等待处理完成
    while true; do
        status=$(python3 scripts/orc_processing_client.py --status --json | python3 -c "
import sys, json
data = json.load(sys.stdin)
print(data.get('is_processing', False))
")
        if [ "$status" = "False" ]; then
            break
        fi
        sleep 60
    done
done
```

## 故障排除

### 服务连接失败
- 确保服务已启动并运行在指定端口
- 检查防火墙设置
- 验证主机和端口配置

### 参数格式错误
- 日期必须是YYYYMMDD格式
- 省份ID必须是正整数
- JSON格式必须正确

### 处理失败
- 检查ORC文件路径是否正确
- 验证Redis连接是否正常
- 查看服务日志获取详细错误信息

## 注意事项

1. **服务状态**: 启动处理前确保服务处于健康状态
2. **参数优先级**: API参数会覆盖配置文件中的默认值
3. **并发处理**: 同时只能运行一个处理任务
4. **资源监控**: 处理大量文件时注意监控系统资源
5. **日志查看**: 详细的处理日志可在服务端查看
