#!/bin/bash
# -*- coding: utf-8 -*-
"""
ORC处理服务使用示例脚本

演示如何使用API接口控制ORC文件处理
"""

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== ORC处理服务使用示例 ===${NC}"
echo ""

# 服务配置
HOST="localhost"
PORT="42001"
BASE_URL="http://${HOST}:${PORT}"

echo -e "${YELLOW}1. 健康检查${NC}"
echo "curl -s ${BASE_URL}/health | python3 -m json.tool"
curl -s "${BASE_URL}/health" | python3 -m json.tool
echo ""

echo -e "${YELLOW}2. 查看服务状态${NC}"
echo "curl -s ${BASE_URL}/stats | python3 -m json.tool"
curl -s "${BASE_URL}/stats" | python3 -m json.tool
echo ""

echo -e "${YELLOW}3. 查看队列状态${NC}"
echo "curl -s ${BASE_URL}/queue/status | python3 -m json.tool"
curl -s "${BASE_URL}/queue/status" | python3 -m json.tool
echo ""

echo -e "${YELLOW}4. 使用默认配置启动处理${NC}"
echo "curl -s -X POST -H 'Content-Type: application/json' -d '{}' ${BASE_URL}/start-processing | python3 -m json.tool"
curl -s -X POST -H "Content-Type: application/json" -d '{}' "${BASE_URL}/start-processing" | python3 -m json.tool
echo ""

echo -e "${YELLOW}5. 使用自定义参数启动处理${NC}"
echo "curl -s -X POST -H 'Content-Type: application/json' -d '{\"start_date\":\"20250701\",\"end_date\":\"20250705\",\"province_ids\":[100,200,300]}' ${BASE_URL}/start-processing | python3 -m json.tool"
curl -s -X POST -H "Content-Type: application/json" -d '{"start_date":"20250701","end_date":"20250705","province_ids":[100,200,300]}' "${BASE_URL}/start-processing" | python3 -m json.tool
echo ""

echo -e "${YELLOW}6. 停止处理${NC}"
echo "curl -s -X POST ${BASE_URL}/stop-processing | python3 -m json.tool"
curl -s -X POST "${BASE_URL}/stop-processing" | python3 -m json.tool
echo ""

echo -e "${GREEN}=== 使用Python客户端脚本 ===${NC}"
echo ""

echo -e "${YELLOW}1. 健康检查${NC}"
echo "python3 scripts/orc_processing_client.py --health"
echo ""

echo -e "${YELLOW}2. 查看状态${NC}"
echo "python3 scripts/orc_processing_client.py --status"
echo ""

echo -e "${YELLOW}3. 使用默认配置启动${NC}"
echo "python3 scripts/orc_processing_client.py --start"
echo ""

echo -e "${YELLOW}4. 指定日期范围启动${NC}"
echo "python3 scripts/orc_processing_client.py --start -s 20250701 -e 20250705"
echo ""

echo -e "${YELLOW}5. 指定省份启动${NC}"
echo "python3 scripts/orc_processing_client.py --start -P 100 200 300"
echo ""

echo -e "${YELLOW}6. 指定日期和省份启动${NC}"
echo "python3 scripts/orc_processing_client.py --start -s 20250701 -e 20250705 -P 100 200"
echo ""

echo -e "${YELLOW}7. 停止处理${NC}"
echo "python3 scripts/orc_processing_client.py --stop"
echo ""

echo -e "${GREEN}=== 使用Shell脚本 ===${NC}"
echo ""

echo -e "${YELLOW}1. 显示帮助${NC}"
echo "./scripts/start_orc_processing.sh --help"
echo ""

echo -e "${YELLOW}2. 健康检查${NC}"
echo "./scripts/start_orc_processing.sh --health"
echo ""

echo -e "${YELLOW}3. 查看状态${NC}"
echo "./scripts/start_orc_processing.sh --status"
echo ""

echo -e "${YELLOW}4. 使用默认配置启动${NC}"
echo "./scripts/start_orc_processing.sh"
echo ""

echo -e "${YELLOW}5. 指定日期范围启动${NC}"
echo "./scripts/start_orc_processing.sh -s 20250701 -e 20250705"
echo ""

echo -e "${YELLOW}6. 指定省份启动${NC}"
echo "./scripts/start_orc_processing.sh -P 100,200,300"
echo ""

echo -e "${YELLOW}7. 指定日期和省份启动${NC}"
echo "./scripts/start_orc_processing.sh -s 20250701 -e 20250705 -P 100,200"
echo ""

echo -e "${YELLOW}8. 停止处理${NC}"
echo "./scripts/start_orc_processing.sh --stop"
echo ""

echo -e "${BLUE}=== 完整的工作流程示例 ===${NC}"
echo ""
echo -e "${GREEN}# 1. 启动服务${NC}"
echo "python3 -m services.orc_mongodb_service.orc_processor_service.main --port 42001"
echo ""
echo -e "${GREEN}# 2. 在另一个终端中，检查服务状态${NC}"
echo "python3 scripts/orc_processing_client.py --health"
echo ""
echo -e "${GREEN}# 3. 启动处理（指定日期和省份）${NC}"
echo "python3 scripts/orc_processing_client.py --start -s 20250701 -e 20250705 -P 100 200"
echo ""
echo -e "${GREEN}# 4. 监控处理进度${NC}"
echo "python3 scripts/orc_processing_client.py --status"
echo ""
echo -e "${GREEN}# 5. 如需停止处理${NC}"
echo "python3 scripts/orc_processing_client.py --stop"
echo ""

echo -e "${BLUE}注意事项:${NC}"
echo "- 确保服务已启动并运行在指定端口"
echo "- 日期格式必须为YYYYMMDD"
echo "- 省份ID必须是正整数"
echo "- 可以通过 --json 参数获取详细的JSON响应"
echo ""
