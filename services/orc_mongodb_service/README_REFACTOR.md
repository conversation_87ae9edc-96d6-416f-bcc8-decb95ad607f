# ORC MongoDB服务重构说明

## 重构概述

根据需求，对 `orc_processor_service` 和 `mongodb_writer_service` 进行了功能调整和重构：

### 主要变更

1. **orc_processor_service** 从微服务改为独立的ORC文件读取程序
2. **mongodb_writer_service** 支持新的简化数据结构和省份分区
3. 数据流程优化：ORC文件 → Milvus PID筛选 → Redis队列 → MongoDB按省份分区写入

## 架构变更详情

### 1. orc_processor_service 重构

#### 变更前（微服务）
- FastAPI HTTP服务
- 提供REST API接口
- 后台任务处理

#### 变更后（独立程序）
- 命令行程序
- 直接执行处理任务
- 移除HTTP服务功能

#### 使用方式

```bash
# 基本使用
python3 services/orc_mongodb_service/orc_processor_service/main.py

# 指定日期范围
python3 services/orc_mongodb_service/orc_processor_service/main.py \
  --start-date 20250629 --end-date 20250630

# 指定省份
python3 services/orc_mongodb_service/orc_processor_service/main.py \
  --province-ids 100 200 210

# 指定配置文件
python3 services/orc_mongodb_service/orc_processor_service/main.py \
  --config configs/orc_mongodb_service/orc_processor_service/development.yaml
```

### 2. mongodb_writer_service 数据结构重构

#### 变更前的数据结构
```json
{
  "uid": 123456789,
  "pid_groups": [
    {
      "timestamp_days": 19723,
      "pids": [1001, 1002, 1003]
    }
  ],
  "pid_count": 300,
  "created_days": 19720,
  "updated_days": 19723,
  "vector_status": {
    "is_stored": false,
    "stored_at_days": null
  },
  "prov_id": 200
}
```

#### 变更后的数据结构
```json
{
  "_id": 123456789,
  "pids": [1001, 1002, 1003],
  "pid_count": 300,
  "updated_days": 19723
}
```

#### 主要改进
1. **简化结构**：移除复杂的嵌套字段
2. **使用_id**：用户ID直接作为MongoDB文档的_id字段
3. **省份分区**：通过集合名称后缀区分省份（如：`user_pid_records_100`）
4. **PID管理**：使用MongoDB自带功能，按时间顺序保留最大条数

### 3. 省份分区机制

#### 集合命名规则
- 基础集合名：`user_pid_records`
- 省份集合名：`user_pid_records_{省份ID}`

#### 示例
```
省份100 → user_pid_records_100
省份200 → user_pid_records_200
省份210 → user_pid_records_210
省份250 → user_pid_records_250
省份531 → user_pid_records_531
省份571 → user_pid_records_571
```

## 配置文件调整

### orc_processor_service 配置变更

#### 移除的配置
```yaml
# 服务配置（已移除）
service:
  name: "orc_processor_service"
  host: "0.0.0.0"
  port: 42001
  workers: 1
```

#### 新增的配置
```yaml
# 程序配置
program:
  name: "orc_processor"
  description: "ORC文件读取和处理程序"
```

### mongodb_writer_service 配置变更

#### 新增配置
```yaml
# 数据处理配置
max_pids_per_user: 300  # 每个用户最多保留的PID数量
```

## 数据流程

### 完整流程
1. **ORC文件读取**：orc_processor 扫描并读取ORC文件
2. **数据处理**：提取用户ID和PID列表
3. **Milvus筛选**：通过Milvus验证PID有效性
4. **数据格式化**：转换为新的数据结构格式
5. **Redis队列**：发送到Redis队列等待处理
6. **MongoDB写入**：mongodb_writer_service从队列读取并按省份分区写入

### 数据转换示例

#### ORC原始数据
```
uid: 123456789
pic_id_list: [1001, 1002, 1003, 1004, 1005]
prov_id: 200
```

#### Milvus筛选后
```
有效PID: [1001, 1003, 1005]  # 假设1002, 1004在Milvus中不存在
```

#### 最终MongoDB文档
```json
{
  "_id": 123456789,
  "pids": [1001, 1003, 1005],
  "pid_count": 3,
  "updated_days": 19723
}
```

#### 存储位置
```
集合: user_pid_records_200  # 根据prov_id=200确定
```

## 运行和监控

### 启动服务

1. **启动MongoDB写入服务**
```bash
python3 services/orc_mongodb_service/mongodb_writer_service/main.py
```

2. **运行ORC处理程序**
```bash
python3 services/orc_mongodb_service/orc_processor_service/main.py \
  --start-date 20250629 --end-date 20250630 --province-ids 100 200 210
```

### 监控和状态检查

#### MongoDB写入服务状态
```bash
curl http://localhost:42002/health
curl http://localhost:42002/stats
curl http://localhost:42002/queue/status
```

#### 日志文件位置
- ORC处理程序：`logs/orc_processor/orc_processor.log`
- MongoDB写入服务：`logs/mongodb_writer_service/mongodb_writer_service.log`

## 性能优化

### 批处理配置
- 用户批处理大小：1000个用户/批次
- PID查询批处理大小：15000个PID/批次
- MongoDB写入批处理大小：1000个文档/批次

### 队列控制
- 暂停阈值：队列长度 > 150
- 恢复阈值：队列长度 < 20
- 检查间隔：5秒

### PID限制
- 每个用户最多保留300个PID
- 按时间顺序保留最新的PID

## 测试验证

运行重构验证测试：
```bash
python3 services/orc_mongodb_service/test_refactor.py
```

测试内容包括：
1. ORC处理器初始化测试
2. MongoDB写入服务初始化测试
3. 新数据结构格式验证
4. 省份集合命名规则验证

## 注意事项

1. **向后兼容性**：新实现不保证向后兼容，建议在新环境中部署
2. **数据迁移**：如需从旧格式迁移数据，需要单独的迁移脚本
3. **监控告警**：建议配置相应的监控和告警机制
4. **资源配置**：根据实际数据量调整批处理大小和连接池配置

## 故障排除

### 常见问题

1. **ORC文件读取失败**
   - 检查文件路径和权限
   - 验证ORC文件格式

2. **Milvus连接失败**
   - 检查Milvus服务状态
   - 验证连接配置

3. **Redis队列堆积**
   - 检查MongoDB写入服务状态
   - 调整队列控制参数

4. **MongoDB写入失败**
   - 检查MongoDB连接和权限
   - 验证集合索引配置
