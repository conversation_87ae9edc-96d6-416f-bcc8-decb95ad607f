#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ORC数据处理微服务

负责ORC文件处理和数据发送到消息队列：
- 自动扫描和处理ORC文件
- 批量读取用户数据
- Milvus PID验证
- Redis队列数据发送
"""

import os
import sys
import asyncio
import json
import time
import glob
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import redis.asyncio as redis
from fastapi import FastAPI, BackgroundTasks
from pydantic import BaseModel
import uvicorn

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from shared.core import ConfigManager, Logger, ExceptionHandler
from shared.database.milvus import MilvusPool, MilvusVectorOperations
from shared.utils import DataProcessor
import pandas as pd
import pyarrow.orc as orc


class StartProcessingRequest(BaseModel):
    """开始处理请求模型"""
    start_date: Optional[str] = None  # YYYYMMDD格式
    end_date: Optional[str] = None    # YYYYMMDD格式
    province_ids: Optional[List[int]] = None  # 省份ID列表


@dataclass
class ORCFileInfo:
    """ORC文件信息"""
    file_path: str
    process_date: str
    prov_id: int
    file_size: int
    created_at: float


@dataclass
class UserBatch:
    """用户批次数据"""
    users: List[Dict[str, Any]]
    batch_id: str
    process_date: str
    prov_id: int
    total_users: int
    total_pids: int


@dataclass
class ProcessingStats:
    """处理统计信息"""
    total_files: int = 0
    processed_files: int = 0
    failed_files: int = 0
    total_users: int = 0
    processed_users: int = 0
    total_pids: int = 0
    valid_pids: int = 0
    processing_time: float = 0.0
    start_time: float = 0.0


class ORCProcessorService:
    """ORC数据处理微服务"""

    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.config = config_manager.get_config("orc_processor_service", default={})
        self.logger = Logger.get_logger(__name__)
        self.exception_handler = ExceptionHandler

        # 初始化组件
        self.data_processor = DataProcessor()
        self.milvus_pool = None
        self.milvus_operations = None
        self.redis_client = None

        # 服务状态
        self.is_running = False
        self.is_processing = False
        self.stats = ProcessingStats(start_time=time.time())

        # 队列控制状态
        self.queue_paused = False
        self.queue_pause_start_time = None

        # 处理配置
        self.batch_size = self.config.get("batch_processing", {}).get("batch_size", 1000)
        self.pid_query_batch_size = self.config.get("batch_processing", {}).get("pid_query_batch_size", 15000)
        self.max_pids_per_user = self.config.get("max_pids_per_user", 300)
        self.enable_milvus_filtering = self.config.get("enable_milvus_filtering", True)

        # 创建FastAPI应用
        self.app = FastAPI(title="ORC Processor Service", version="2.0.0")
        self._setup_routes()
    
    async def initialize(self):
        """初始化服务"""
        try:
            self.logger.info("初始化ORC数据处理微服务...")

            # 初始化Milvus连接池和操作类（仅在启用过滤时）
            if self.enable_milvus_filtering:
                self.milvus_pool = MilvusPool(config_manager=self.config_manager)
                milvus_config = self.config.get("milvus", {})
                collections = milvus_config.get("collections", {})
                content_collection = collections.get("content_collection", "content_tower_collection_20250616")
                self.milvus_operations = MilvusVectorOperations(self.milvus_pool, content_collection)
                self.logger.info(f"Milvus PID过滤已启用，使用集合: {content_collection}")
            else:
                self.logger.info("Milvus PID过滤已禁用，跳过Milvus初始化")

            # 初始化Redis连接
            redis_config = self.config.get("redis", {})
            self.redis_client = redis.Redis(
                host=redis_config.get("host", "localhost"),
                port=redis_config.get("port", 6379),
                db=redis_config.get("db", 0),
                decode_responses=True
            )

            # 测试Redis连接
            await self.redis_client.ping()

            self.is_running = True
            self.logger.info("ORC数据处理微服务初始化完成")

        except Exception as e:
            self.logger.error(f"服务初始化失败: {e}")
            raise
    
    async def shutdown(self):
        """关闭服务"""
        try:
            self.logger.info("关闭ORC数据处理微服务...")
            self.is_running = False
            
            if self.redis_client:
                await self.redis_client.close()
            
            if self.milvus_pool:
                await self.milvus_pool.close()
            
            self.logger.info("ORC数据处理微服务已关闭")
            
        except Exception as e:
            self.logger.error(f"服务关闭失败: {e}")
    
    def _setup_routes(self):
        """设置API路由"""

        @self.app.get("/health")
        async def health_check():
            """健康检查"""
            return {
                "status": "healthy" if self.is_running else "unhealthy",
                "service": "orc_processor_service",
                "version": "2.0.0",
                "uptime": time.time() - self.stats.start_time,
                "is_processing": self.is_processing,
                "stats": asdict(self.stats)
            }

        @self.app.get("/stats")
        async def get_stats():
            """获取统计信息"""
            return {
                "stats": asdict(self.stats),
                "is_processing": self.is_processing,
                "queue_paused": self.queue_paused,
                "config": {
                    "batch_size": self.batch_size,
                    "pid_query_batch_size": self.pid_query_batch_size,
                    "max_pids_per_user": self.max_pids_per_user,
                    "enable_milvus_filtering": self.enable_milvus_filtering
                }
            }

        @self.app.get("/queue/status")
        async def get_queue_status():
            """获取队列状态"""
            try:
                redis_config = self.config.get("redis", {})
                queue_name = redis_config.get("queue_name", "mongodb_write_queue")
                queue_length = await self.redis_client.llen(queue_name)

                queue_control = redis_config.get("queue_control", {})
                pause_threshold = queue_control.get("pause_threshold", 150)
                resume_threshold = queue_control.get("resume_threshold", 20)

                current_time = time.time()
                pause_duration = None
                if self.queue_paused and self.queue_pause_start_time:
                    pause_duration = current_time - self.queue_pause_start_time

                return {
                    "queue_name": queue_name,
                    "queue_length": queue_length,
                    "is_paused": self.queue_paused,
                    "pause_duration": pause_duration,
                    "thresholds": {
                        "pause_threshold": pause_threshold,
                        "resume_threshold": resume_threshold
                    },
                    "status": "paused" if self.queue_paused else "running"
                }
            except Exception as e:
                return {"error": str(e)}

        @self.app.post("/start-processing")
        async def start_processing(request: StartProcessingRequest, background_tasks: BackgroundTasks):
            """开始自动处理ORC文件"""
            if self.is_processing:
                return {"status": "already_running", "message": "处理已在进行中"}

            # 验证日期格式
            if request.start_date:
                try:
                    datetime.strptime(request.start_date, "%Y%m%d")
                except ValueError:
                    return {"status": "error", "message": "start_date格式错误，应为YYYYMMDD"}

            if request.end_date:
                try:
                    datetime.strptime(request.end_date, "%Y%m%d")
                except ValueError:
                    return {"status": "error", "message": "end_date格式错误，应为YYYYMMDD"}

            # 验证省份ID
            if request.province_ids:
                if not all(isinstance(pid, int) and pid > 0 for pid in request.province_ids):
                    return {"status": "error", "message": "province_ids必须是正整数列表"}

            background_tasks.add_task(self._start_auto_processing, request)

            return {
                "status": "started",
                "message": "开始自动处理ORC文件",
                "parameters": {
                    "start_date": request.start_date,
                    "end_date": request.end_date,
                    "province_ids": request.province_ids
                }
            }

        @self.app.post("/stop-processing")
        async def stop_processing():
            """停止自动处理"""
            self.is_processing = False
            return {"status": "stopped", "message": "已停止自动处理"}
        
    async def _start_auto_processing(self, request: Optional[StartProcessingRequest] = None):
        """开始自动处理ORC文件"""
        try:
            self.is_processing = True
            self.logger.info("开始自动处理ORC文件...")

            # 获取需要处理的ORC文件列表
            orc_files = await self._discover_orc_files(request)

            if not orc_files:
                self.logger.info("没有找到需要处理的ORC文件")
                return

            self.stats.total_files = len(orc_files)
            self.logger.info(f"发现 {len(orc_files)} 个ORC文件需要处理")

            # 逐个处理ORC文件
            for orc_file_info in orc_files:
                if not self.is_processing:
                    self.logger.info("处理已被停止")
                    break

                await self._process_single_orc_file(orc_file_info)
                self.stats.processed_files += 1

                # 检查队列状态，必要时等待
                await self._wait_for_queue_space()

            self.stats.processing_time = time.time() - self.stats.start_time
            self.logger.info(f"自动处理完成，处理了 {self.stats.processed_files}/{self.stats.total_files} 个文件")

        except Exception as e:
            self.logger.error(f"自动处理失败: {e}")
            self.stats.failed_files += 1
        finally:
            self.is_processing = False
    
    async def _discover_orc_files(self, request: Optional[StartProcessingRequest] = None) -> List[ORCFileInfo]:
        """发现需要处理的ORC文件"""
        try:
            orc_files = []

            # 获取配置，优先使用请求参数
            if request and request.start_date:
                start_date = request.start_date
            else:
                start_date = self.config.get("start_date", "20250629")

            if request and request.end_date:
                end_date = request.end_date
            else:
                end_date = self.config.get("end_date", "20250630")

            if request and request.province_ids:
                province_ids = request.province_ids
            else:
                province_ids = self.config.get("province_ids", [200])

            orc_base_path = self.config.get("orc_base_path", "/workdir/hive_data/tw_user_pic_daily_aggregation")
            orc_file_pattern = self.config.get("orc_file_pattern", "*")

            self.logger.info(f"扫描ORC文件: 日期范围 {start_date}-{end_date}, 省份 {province_ids}")
            if request:
                self.logger.info("使用API请求参数覆盖配置文件设置")

            # 生成日期范围
            current_date = datetime.strptime(start_date, "%Y%m%d")
            end_date_obj = datetime.strptime(end_date, "%Y%m%d")

            while current_date <= end_date_obj:
                date_str = current_date.strftime("%Y%m%d")

                for prov_id in province_ids:
                    # 构建文件路径模式
                    file_pattern = os.path.join(
                        orc_base_path,
                        f"prov_id={prov_id}",
                        f"statis_ymd={date_str}",
                        orc_file_pattern
                    )

                    # 查找匹配的文件
                    matching_files = glob.glob(file_pattern)

                    for file_path in matching_files:
                        if os.path.isfile(file_path):
                            file_info = ORCFileInfo(
                                file_path=file_path,
                                process_date=date_str,
                                prov_id=prov_id,
                                file_size=os.path.getsize(file_path),
                                created_at=time.time()
                            )
                            orc_files.append(file_info)
                            self.logger.debug(f"发现ORC文件: {file_path}")

                current_date += timedelta(days=1)

            self.logger.info(f"总共发现 {len(orc_files)} 个ORC文件")
            return orc_files

        except Exception as e:
            self.logger.error(f"发现ORC文件失败: {e}")
            return []
    
    async def _process_single_orc_file(self, orc_file_info: ORCFileInfo):
        """处理单个ORC文件"""
        try:
            self.logger.info(f"开始处理ORC文件: {orc_file_info.file_path}")
            file_start_time = time.time()

            # 读取ORC文件
            df = self._read_orc_file(orc_file_info.file_path)
            total_rows = len(df)

            if total_rows == 0:
                self.logger.warning(f"ORC文件为空: {orc_file_info.file_path}")
                return

            self.logger.info(f"ORC文件包含 {total_rows} 行数据")

            # 分批处理用户数据
            processed_users = 0
            for batch_start in range(0, total_rows, self.batch_size):
                if not self.is_processing:
                    break

                batch_end = min(batch_start + self.batch_size, total_rows)
                batch_df = df.iloc[batch_start:batch_end]

                # 处理当前批次
                user_batch = await self._process_user_batch(
                    batch_df,
                    orc_file_info.process_date,
                    orc_file_info.prov_id,
                    f"batch_{batch_start}_{batch_end}"
                )

                if user_batch and user_batch.users:
                    # 发送到Redis队列
                    await self._send_batch_to_queue(user_batch)
                    processed_users += len(user_batch.users)
                    self.stats.processed_users += len(user_batch.users)

                # 检查队列状态
                await self._wait_for_queue_space()

            processing_time = time.time() - file_start_time
            self.logger.info(f"ORC文件处理完成: {orc_file_info.file_path}, "
                           f"处理用户数: {processed_users}/{total_rows}, "
                           f"耗时: {processing_time:.2f}秒")

        except Exception as e:
            self.logger.error(f"处理ORC文件失败: {orc_file_info.file_path}, 错误: {e}")
            self.stats.failed_files += 1
            raise

    async def _process_user_batch(self, batch_df: pd.DataFrame, process_date: str,
                                 prov_id: int, batch_id: str) -> Optional[UserBatch]:
        """处理用户批次数据"""
        try:
            users = []
            total_pids = 0

            # 获取列名映射配置
            column_mapping = self.config.get("column_mapping", {})
            uid_columns = column_mapping.get("uid_columns", ["id", "uid", "user_id"])
            pid_columns = column_mapping.get("pid_columns", ["pic_id_list", "pid_list", "pid"])

            # 处理每个用户
            for _, row in batch_df.iterrows():
                # 提取UID
                uid = None
                for col in uid_columns:
                    if col in row and pd.notna(row[col]):
                        uid = int(row[col])
                        break

                if uid is None:
                    continue

                # 提取PID列表
                pid_list = []
                for col in pid_columns:
                    if col in row and pd.notna(row[col]):
                        pids = row[col]
                        if isinstance(pids, str):
                            # 如果是字符串，尝试解析
                            try:
                                if pids.startswith('[') and pids.endswith(']'):
                                    pid_list = json.loads(pids)
                                else:
                                    pid_list = [int(x.strip()) for x in pids.split(',') if x.strip()]
                            except:
                                continue
                        elif isinstance(pids, (list, tuple)):
                            pid_list = [int(p) for p in pids if pd.notna(p)]
                        elif isinstance(pids, (int, float)):
                            pid_list = [int(pids)]
                        break

                if not pid_list:
                    continue

                # 限制PID数量
                if len(pid_list) > self.max_pids_per_user:
                    pid_list = pid_list[:self.max_pids_per_user]

                total_pids += len(pid_list)

                user_data = {
                    "uid": uid,
                    "pid_list": pid_list,
                    "process_date": process_date,
                    "prov_id": prov_id,
                    "processed_at": int(time.time())
                }
                users.append(user_data)

            if not users:
                return None

            # Milvus PID过滤
            if self.enable_milvus_filtering and self.milvus_operations:
                users = await self._filter_users_by_milvus(users)

            if not users:
                return None

            user_batch = UserBatch(
                users=users,
                batch_id=batch_id,
                process_date=process_date,
                prov_id=prov_id,
                total_users=len(users),
                total_pids=sum(len(u["pid_list"]) for u in users)
            )

            self.logger.debug(f"批次处理完成: {batch_id}, 用户数: {len(users)}, PID数: {user_batch.total_pids}")
            return user_batch

        except Exception as e:
            self.logger.error(f"处理用户批次失败: {batch_id}, 错误: {e}")
            return None

    async def _filter_users_by_milvus(self, users: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """通过Milvus过滤用户的PID"""
        try:
            # 收集所有PID
            all_pids = []
            for user in users:
                all_pids.extend(user["pid_list"])

            if not all_pids:
                return users

            # 去重PID
            unique_pids = list(set(all_pids))
            self.logger.debug(f"开始Milvus PID过滤，总PID数: {len(unique_pids)}")

            # 分批查询Milvus
            valid_pids = set()
            for batch_start in range(0, len(unique_pids), self.pid_query_batch_size):
                batch_end = min(batch_start + self.pid_query_batch_size, len(unique_pids))
                pid_batch = unique_pids[batch_start:batch_end]

                # 查询Milvus
                result = self.milvus_operations.query_vectors(
                    ids=pid_batch,
                    output_fields=["item_id"]
                )

                if result.success and result.results:
                    batch_valid_pids = {r.get("item_id") for r in result.results if r.get("item_id")}
                    valid_pids.update(batch_valid_pids)

            self.logger.debug(f"Milvus查询完成，有效PID数: {len(valid_pids)}/{len(unique_pids)}")
            self.stats.total_pids += len(unique_pids)
            self.stats.valid_pids += len(valid_pids)

            # 过滤用户的PID
            filtered_users = []
            for user in users:
                filtered_pids = [pid for pid in user["pid_list"] if pid in valid_pids]
                if filtered_pids:
                    user["pid_list"] = filtered_pids
                    filtered_users.append(user)

            self.logger.debug(f"PID过滤完成，保留用户数: {len(filtered_users)}/{len(users)}")
            return filtered_users

        except Exception as e:
            self.logger.error(f"Milvus PID过滤失败: {e}")
            # 出错时返回原始用户数据
            return users

    def _read_orc_file(self, file_path: str) -> pd.DataFrame:
        """读取ORC文件"""
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"ORC文件不存在: {file_path}")

            orc_file = orc.ORCFile(file_path)
            table = orc_file.read()
            df = table.to_pandas()

            self.logger.debug(f"成功读取ORC文件: {file_path}, 行数: {len(df)}")
            return df

        except Exception as e:
            self.logger.error(f"读取ORC文件失败: {file_path}, 错误: {e}")
            raise

    async def _send_batch_to_queue(self, user_batch: UserBatch):
        """发送用户批次到Redis队列"""
        try:
            queue_name = self.config.get("redis", {}).get("queue_name", "mongodb_write_queue")

            # 序列化批次数据
            batch_json = json.dumps(asdict(user_batch), ensure_ascii=False)

            # 发送到Redis队列
            await self.redis_client.lpush(queue_name, batch_json)

            self.logger.debug(f"批次数据已发送到队列: {user_batch.batch_id}, 用户数: {user_batch.total_users}")

        except Exception as e:
            self.logger.error(f"发送批次到队列失败: {e}")
            raise

    async def _check_queue_length(self) -> bool:
        """
        检查Redis队列长度，决定是否需要暂停处理

        Returns:
            bool: True表示可以继续处理，False表示需要暂停
        """
        try:
            redis_config = self.config.get("redis", {})
            queue_name = redis_config.get("queue_name", "mongodb_write_queue")
            queue_control = redis_config.get("queue_control", {})

            # 获取队列长度
            queue_length = await self.redis_client.llen(queue_name)

            # 获取配置参数
            pause_threshold = queue_control.get("pause_threshold", 8000)
            resume_threshold = queue_control.get("resume_threshold", 5000)

            current_time = time.time()

            # 检查是否需要暂停
            if not self.queue_paused and queue_length >= pause_threshold:
                self.queue_paused = True
                self.queue_pause_start_time = current_time
                self.logger.warning(f"Redis队列长度达到暂停阈值 {pause_threshold}，当前长度: {queue_length}，暂停ORC处理")
                return False

            # 检查是否可以恢复
            elif self.queue_paused:
                # 只根据恢复阈值决定是否恢复
                if queue_length <= resume_threshold:
                    self.queue_paused = False
                    pause_duration = current_time - (self.queue_pause_start_time or current_time)
                    self.queue_pause_start_time = None
                    self.logger.info(f"Redis队列长度降至恢复阈值 {resume_threshold}，当前长度: {queue_length}，恢复ORC处理，暂停时长: {pause_duration:.1f}秒")
                    return True
                else:
                    # 仍需要等待，直到队列长度降到恢复阈值以下
                    wait_time = current_time - (self.queue_pause_start_time or current_time)
                    self.logger.debug(f"队列仍在暂停中，当前长度: {queue_length}，已等待: {wait_time:.1f}秒")
                    return False

            # 正常状态，可以继续处理
            return True

        except Exception as e:
            self.logger.error(f"检查队列长度失败: {e}")
            # 出错时允许继续处理，避免阻塞
            return True

    async def _wait_for_queue_space(self):
        """等待队列有空间可用"""
        redis_config = self.config.get("redis", {})
        queue_control = redis_config.get("queue_control", {})
        check_interval = queue_control.get("check_interval", 5)

        while self.is_running:
            if await self._check_queue_length():
                break

            # 等待一段时间后再检查
            await asyncio.sleep(check_interval)

    def get_processing_status(self) -> Dict[str, Any]:
        """获取处理状态"""
        return {
            "is_running": self.is_running,
            "is_processing": self.is_processing,
            "queue_paused": self.queue_paused,
            "stats": asdict(self.stats),
            "config": {
                "batch_size": self.batch_size,
                "pid_query_batch_size": self.pid_query_batch_size,
                "max_pids_per_user": self.max_pids_per_user,
                "enable_milvus_filtering": self.enable_milvus_filtering
            }
        }
    
    def run(self, host: str = "0.0.0.0", port: int = 8001):
        """运行服务"""
        uvicorn.run(
            self.app,
            host=host,
            port=port,
            log_level="info"
        )


async def create_service():
    """创建服务实例"""
    config_manager = ConfigManager()
    service = ORCProcessorService(config_manager)
    await service.initialize()
    return service


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="ORC数据处理微服务")
    parser.add_argument("--host", default="0.0.0.0", help="服务主机地址")
    parser.add_argument("--port", type=int, default=8001, help="服务端口")
    parser.add_argument("--config", help="配置文件路径")
    
    args = parser.parse_args()
    
    # 设置配置文件路径
    if args.config:
        os.environ['USER_DF_CONFIG_FILE'] = args.config
    
    # 创建并运行服务
    async def main():
        service = await create_service()
        try:
            service.run(host=args.host, port=args.port)
        finally:
            await service.shutdown()
    
    asyncio.run(main())
