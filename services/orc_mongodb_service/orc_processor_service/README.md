# ORC处理微服务 - 重构完成

## 概述

ORC处理微服务已完成完全重构，现在支持以下核心功能：

- **自动ORC文件发现和处理**: 根据配置自动扫描和处理ORC文件
- **批次用户数据处理**: 按配置的batch_size分批处理用户数据
- **Milvus PID验证**: 可选的PID存在性验证（通过配置启用/禁用）
- **Redis队列集成**: 将处理后的数据发送到Redis队列
- **配置文件兼容性**: 保持与现有配置文件和服务启动器的兼容性

## 主要改进

### 1. 自动化处理
- **之前**: 只能通过API调用处理单个文件
- **现在**: 自动发现并顺序处理配置范围内的所有ORC文件

### 2. 批次处理
- **之前**: 一次性处理所有数据
- **现在**: 按配置的batch_size（默认1000）分批处理用户数据

### 3. Milvus集成
- **之前**: 没有PID验证
- **现在**: 可选的Milvus PID存在性验证，只保留存在的PID

### 4. Redis队列
- **之前**: 没有队列集成
- **现在**: 自动将处理结果发送到Redis队列，支持队列长度监控

## 配置说明

服务使用 `configs/orc_mongodb_service/orc_processor_service/development.yaml` 配置文件：

```yaml
# 日期范围
start_date: "20250629"
end_date: "20250630"

# 省份ID列表
province_ids: [200]

# ORC文件路径
orc_base_path: "/workdir/hive_data/tw_user_pic_daily_aggregation"

# 批处理配置
batch_processing:
  batch_size: 1000              # 用户批次大小
  pid_query_batch_size: 15000   # PID查询批次大小

# 数据处理配置
max_pids_per_user: 300          # 每用户最大PID数

# Milvus PID过滤
enable_milvus_filtering: false  # 是否启用PID验证
```

## API接口

### 健康检查
```bash
GET /health
```

### 获取统计信息
```bash
GET /stats
```

### 获取队列状态
```bash
GET /queue/status
```

### 开始自动处理
```bash
POST /start-processing
```

### 停止处理
```bash
POST /stop-processing
```

## 启动方式

### 1. 单独启动
```bash
cd /path/to/User-DF
USER_DF_CONFIG_FILE=configs/orc_mongodb_service/orc_processor_service/development.yaml \
python3 -m services.orc_mongodb_service.orc_processor_service.main --port 42001
```

### 2. 通过服务管理器启动
```bash
cd /path/to/User-DF
python3 services/orc_mongodb_service/start_services.py \
  --config configs/orc_mongodb_service/development.yaml
```

## 处理流程

1. **文件发现**: 根据日期范围和省份ID扫描ORC文件
2. **批次读取**: 每次读取batch_size个用户记录
3. **数据提取**: 提取用户ID和PID列表
4. **PID验证**: （可选）通过Milvus验证PID存在性
5. **数据发送**: 将处理后的用户批次发送到Redis队列
6. **状态更新**: 更新处理统计信息

## 数据结构

### 用户批次数据
```python
{
    "users": [
        {
            "uid": 12345,
            "pid_list": [101, 102, 103],
            "process_date": "20250630",
            "prov_id": 200,
            "processed_at": 1627123456
        }
    ],
    "batch_id": "batch_0_1000",
    "process_date": "20250630",
    "prov_id": 200,
    "total_users": 1000,
    "total_pids": 15000
}
```

## 监控和日志

- 服务状态通过 `/stats` 接口获取
- 处理进度实时更新
- 详细日志记录处理过程
- 支持队列长度监控和背压控制

## 兼容性

- ✅ 与现有配置文件完全兼容
- ✅ 与服务启动器 `start_services.py` 兼容
- ✅ 保持原有API接口
- ✅ 支持原有的端口配置（42001）

## 测试验证

重构后的服务已通过以下测试：
- ✅ 服务初始化测试
- ✅ ORC文件发现测试
- ✅ 用户批次处理测试
- ✅ 配置文件兼容性测试
- ✅ API接口测试

服务现在完全满足用户需求：
> "顺序读取并处理orc文件。每次按照要求batch_size读取指定数量的用户数据，并对用户数据中的pid在milvus中进行查询，并只保留能够查询到的pid（不需要获取到具体向量）。随后将这些用户数据存入至Redis队列中。"
