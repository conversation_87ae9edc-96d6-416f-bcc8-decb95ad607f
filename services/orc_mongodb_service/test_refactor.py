#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重构验证测试脚本

测试重构后的ORC处理程序和MongoDB写入服务
"""

import os
import sys
import asyncio
import json
import time
from typing import Dict, List, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from shared.core import ConfigManager, Logger
from services.orc_mongodb_service.orc_processor_service.service import ORCProcessor
from services.orc_mongodb_service.mongodb_writer_service.service import MongoDBWriterService


class RefactorTester:
    """重构验证测试器"""
    
    def __init__(self):
        self.logger = Logger.get_logger(__name__)
        self.config_manager = ConfigManager()
    
    async def test_orc_processor_initialization(self):
        """测试ORC处理器初始化"""
        try:
            self.logger.info("=== 测试ORC处理器初始化 ===")
            
            processor = ORCProcessor(self.config_manager)
            await processor.initialize()
            
            # 检查处理器状态
            status = processor.get_processing_status()
            self.logger.info(f"处理器状态: {status}")
            
            await processor.shutdown()
            self.logger.info("✓ ORC处理器初始化测试通过")
            return True
            
        except Exception as e:
            self.logger.error(f"✗ ORC处理器初始化测试失败: {e}")
            return False
    
    async def test_mongodb_writer_initialization(self):
        """测试MongoDB写入服务初始化"""
        try:
            self.logger.info("=== 测试MongoDB写入服务初始化 ===")
            
            writer = MongoDBWriterService(self.config_manager)
            await writer.initialize()
            
            # 检查服务状态
            self.logger.info("MongoDB写入服务初始化成功")
            
            await writer.shutdown()
            self.logger.info("✓ MongoDB写入服务初始化测试通过")
            return True
            
        except Exception as e:
            self.logger.error(f"✗ MongoDB写入服务初始化测试失败: {e}")
            return False
    
    def test_data_structure_format(self):
        """测试新数据结构格式"""
        try:
            self.logger.info("=== 测试新数据结构格式 ===")
            
            # 模拟新的用户数据结构
            sample_user_data = {
                "_id": 123456789,
                "pids": [1001, 1002, 1003],
                "pid_count": 3,
                "updated_days": int(time.time()) // 86400
            }
            
            # 验证必需字段
            required_fields = ["_id", "pids", "pid_count", "updated_days"]
            for field in required_fields:
                if field not in sample_user_data:
                    raise ValueError(f"缺少必需字段: {field}")
            
            # 验证数据类型
            if not isinstance(sample_user_data["_id"], int):
                raise ValueError("_id字段必须是整数")
            
            if not isinstance(sample_user_data["pids"], list):
                raise ValueError("pids字段必须是列表")
            
            if not isinstance(sample_user_data["pid_count"], int):
                raise ValueError("pid_count字段必须是整数")
            
            if not isinstance(sample_user_data["updated_days"], int):
                raise ValueError("updated_days字段必须是整数")
            
            self.logger.info(f"样本数据结构: {sample_user_data}")
            self.logger.info("✓ 新数据结构格式测试通过")
            return True
            
        except Exception as e:
            self.logger.error(f"✗ 新数据结构格式测试失败: {e}")
            return False
    
    def test_province_collection_naming(self):
        """测试省份集合命名规则"""
        try:
            self.logger.info("=== 测试省份集合命名规则 ===")
            
            base_collection = "user_pid_records"
            test_provinces = [100, 200, 210, 250, 531, 571]
            
            for prov_id in test_provinces:
                expected_collection = f"{base_collection}_{prov_id}"
                self.logger.info(f"省份 {prov_id} -> 集合名称: {expected_collection}")
            
            self.logger.info("✓ 省份集合命名规则测试通过")
            return True
            
        except Exception as e:
            self.logger.error(f"✗ 省份集合命名规则测试失败: {e}")
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        self.logger.info("开始重构验证测试...")
        
        test_results = []
        
        # 测试ORC处理器初始化
        result1 = await self.test_orc_processor_initialization()
        test_results.append(("ORC处理器初始化", result1))
        
        # 测试MongoDB写入服务初始化
        result2 = await self.test_mongodb_writer_initialization()
        test_results.append(("MongoDB写入服务初始化", result2))
        
        # 测试数据结构格式
        result3 = self.test_data_structure_format()
        test_results.append(("数据结构格式", result3))
        
        # 测试省份集合命名规则
        result4 = self.test_province_collection_naming()
        test_results.append(("省份集合命名规则", result4))
        
        # 汇总测试结果
        self.logger.info("\n=== 测试结果汇总 ===")
        passed = 0
        total = len(test_results)
        
        for test_name, result in test_results:
            status = "✓ 通过" if result else "✗ 失败"
            self.logger.info(f"{test_name}: {status}")
            if result:
                passed += 1
        
        self.logger.info(f"\n总计: {passed}/{total} 个测试通过")
        
        if passed == total:
            self.logger.info("🎉 所有测试通过！重构成功！")
        else:
            self.logger.warning(f"⚠️  有 {total - passed} 个测试失败，需要进一步检查")
        
        return passed == total


async def main():
    """主函数"""
    try:
        # 设置配置文件
        os.environ['USER_DF_CONFIG_FILE'] = 'configs/orc_mongodb_service/development.yaml'
        
        tester = RefactorTester()
        success = await tester.run_all_tests()
        
        if success:
            print("\n✅ 重构验证测试全部通过！")
            sys.exit(0)
        else:
            print("\n❌ 重构验证测试存在失败项，请检查日志")
            sys.exit(1)
            
    except Exception as e:
        print(f"测试运行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
